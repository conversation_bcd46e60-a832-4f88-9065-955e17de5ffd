#!/usr/bin/env python3
"""
Wrapper Python per avviare IMDG Manager come servizio Windows
"""
import os
import sys
import subprocess
import time

def main():
    # Cambia nella directory del progetto
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Percorso del Python del virtual environment
    python_exe = os.path.join(script_dir, "IMDG", "Scripts", "python.exe")
    app_py = os.path.join(script_dir, "app.py")
    
    # Verifica che i file esistano
    if not os.path.exists(python_exe):
        print(f"ERRORE: Python non trovato in {python_exe}")
        sys.exit(1)
        
    if not os.path.exists(app_py):
        print(f"ERRORE: app.py non trovato in {app_py}")
        sys.exit(1)
    
    print(f"Avvio IMDG Manager...")
    print(f"Python: {python_exe}")
    print(f"App: {app_py}")
    print(f"Directory: {script_dir}")
    
    try:
        # Avvia l'applicazione Flask
        subprocess.run([python_exe, app_py], check=True)
    except KeyboardInterrupt:
        print("Servizio interrotto dall'utente")
    except Exception as e:
        print(f"Errore durante l'esecuzione: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
