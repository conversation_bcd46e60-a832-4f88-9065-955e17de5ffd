@echo off
REM Crea un'attività pianificata che si avvia all'avvio del sistema

echo ========================================
echo Creazione Attività di Sistema IMDG
echo ========================================

REM Elimina le attività esistenti
schtasks /delete /tn "IMDG Manager" /f >nul 2>&1
schtasks /delete /tn "IMDG Manager System" /f >nul 2>&1

REM Crea l'attività che si avvia all'avvio del sistema
schtasks /create /tn "IMDG Manager System" /tr "powershell.exe -ExecutionPolicy Bypass -File \"%~dp0start_service.ps1\"" /sc onstart /ru "SYSTEM" /f

if %errorLevel% == 0 (
    echo [OK] Attività di sistema creata con successo!
    echo.
    echo L'applicazione IMDG Manager si avvierà automaticamente
    echo all'avvio del sistema come servizio di sistema.
    echo.
    echo Per avviare manualmente:
    echo   schtasks /run /tn "IMDG Manager System"
    echo.
    echo Per eliminare:
    echo   schtasks /delete /tn "IMDG Manager System" /f
    echo.
    echo Per vedere lo stato:
    echo   schtasks /query /tn "IMDG Manager System"
    echo.
    echo NOTA: Questo si avvia come SYSTEM, quindi funziona anche
    echo quando nessun utente è collegato.
) else (
    echo [ERRORE] Impossibile creare l'attività di sistema!
    echo Prova ad eseguire come Amministratore.
)

echo.
pause
