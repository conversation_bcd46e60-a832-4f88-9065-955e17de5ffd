#!/usr/bin/env python3
"""
Servizio Windows nativo per IMDG Manager
"""
import os
import sys
import time
import subprocess
import win32serviceutil
import win32service
import win32event
import servicemanager

class IMDGManagerService(win32serviceutil.ServiceFramework):
    _svc_name_ = "IMDGManagerService"
    _svc_display_name_ = "IMDG Manager Service"
    _svc_description_ = "Servizio per l'applicazione IMDG Manager Flask"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.process = None

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        if self.process:
            self.process.terminate()

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ''))
        self.main()

    def main(self):
        # Ottieni la directory del servizio
        service_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(service_dir)
        
        # Percorsi
        python_exe = os.path.join(service_dir, "IMDG", "Scripts", "python.exe")
        app_py = os.path.join(service_dir, "app.py")
        
        try:
            # Avvia l'applicazione Flask
            self.process = subprocess.Popen([python_exe, app_py])
            
            # Aspetta che il servizio venga fermato
            win32event.WaitForSingleObject(self.hWaitStop, win32event.INFINITE)
            
        except Exception as e:
            servicemanager.LogErrorMsg(f"Errore nel servizio: {e}")

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(IMDGManagerService)
