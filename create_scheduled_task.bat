@echo off
REM Crea un'attività pianificata per avviare IMDG Manager all'avvio

echo ========================================
echo Creazione Attività Pianificata IMDG
echo ========================================

REM Elimina l'attività se esiste già
schtasks /delete /tn "IMDG Manager" /f >nul 2>&1

REM Crea la nuova attività pianificata
schtasks /create /tn "IMDG Manager" /tr "powershell.exe -ExecutionPolicy Bypass -File \"%~dp0start_service.ps1\"" /sc onlogon /ru "%USERNAME%" /f

if %errorLevel% == 0 (
    echo [OK] Attività pianificata creata con successo!
    echo.
    echo L'applicazione IMDG Manager si avvierà automaticamente
    echo quando l'utente %USERNAME% effettua l'accesso.
    echo.
    echo Per avviare manualmente l'attività:
    echo   schtasks /run /tn "IMDG Manager"
    echo.
    echo Per eliminare l'attività:
    echo   schtasks /delete /tn "IMDG Manager" /f
    echo.
    echo Per vedere lo stato dell'attività:
    echo   schtasks /query /tn "IMDG Manager"
) else (
    echo [ERRORE] Impossibile creare l'attività pianificata!
)

echo.
pause
